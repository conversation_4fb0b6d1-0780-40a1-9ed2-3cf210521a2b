import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import '../../../core/theme/app_colors.dart';
import '../../bloc/household/household_bloc.dart';
import '../../bloc/household/household_event.dart';
import '../../bloc/household/household_state.dart';

/// Household onboarding page that appears after authentication
class HouseholdOnboardingPage extends StatefulWidget {
  const HouseholdOnboardingPage({super.key});

  @override
  State<HouseholdOnboardingPage> createState() =>
      _HouseholdOnboardingPageState();
}

class _HouseholdOnboardingPageState extends State<HouseholdOnboardingPage> {
  @override
  void initState() {
    super.initState();
    // Create default household when page loads
    _createDefaultHousehold();
  }

  void _createDefaultHousehold() {
    // TODO: Get current user from auth state
    // For now, use a mock user ID
    context.read<HouseholdBloc>().add(
          const CreateHouseholdEvent(
            name: '<PERSON> <PERSON><PERSON><PERSON>',
            description: '<PERSON>oyer par défaut',
            ownerId: 'mock-user-id',
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<HouseholdBloc, HouseholdState>(
      listener: (context, state) {
        if (state is HouseholdCreated) {
          // Navigate directly to onboarding after household is created
          context.go('/onboarding');
        } else if (state is HouseholdError) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: AppColors.grey50,
        body: BlocBuilder<HouseholdBloc, HouseholdState>(
          builder: (context, state) {
            if (state is HouseholdLoading) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('Configuration de votre foyer...'),
                  ],
                ),
              );
            }

            return const Center(
              child: Text('Configuration en cours...'),
            );
          },
        ),
      ),
    );
  }
}
