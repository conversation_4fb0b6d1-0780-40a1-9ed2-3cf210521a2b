import 'package:equatable/equatable.dart';

/// Household entity representing a family unit in MedyTrack
class Household extends Equatable {
  final String id;
  final String name;
  final String? description;
  final String? inviteCode;
  final String ownerId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;

  const Household({
    required this.id,
    required this.name,
    this.description,
    this.inviteCode,
    required this.ownerId,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
  });

  /// Create a copy of this household with updated fields
  Household copyWith({
    String? id,
    String? name,
    String? description,
    String? inviteCode,
    String? ownerId,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
  }) {
    return Household(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      inviteCode: inviteCode ?? this.inviteCode,
      ownerId: ownerId ?? this.ownerId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Create a household from JSON
  factory Household.fromJson(Map<String, dynamic> json) {
    return Household(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      inviteCode: json['invite_code'] as String?,
      ownerId: json['owner_id'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// Convert household to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'invite_code': inviteCode,
      'owner_id': ownerId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        inviteCode,
        ownerId,
        createdAt,
        updatedAt,
        isActive,
      ];

  @override
  String toString() {
    return 'Household(id: $id, name: $name, ownerId: $ownerId, isActive: $isActive)';
  }
}

/// Household member entity representing a user's membership in a household
class HouseholdMember extends Equatable {
  final String id;
  final String householdId;
  final String userId;
  final String role; // 'owner', 'admin', 'member'
  final DateTime joinedAt;
  final bool isActive;

  const HouseholdMember({
    required this.id,
    required this.householdId,
    required this.userId,
    required this.role,
    required this.joinedAt,
    this.isActive = true,
  });

  /// Create a copy of this household member with updated fields
  HouseholdMember copyWith({
    String? id,
    String? householdId,
    String? userId,
    String? role,
    DateTime? joinedAt,
    bool? isActive,
  }) {
    return HouseholdMember(
      id: id ?? this.id,
      householdId: householdId ?? this.householdId,
      userId: userId ?? this.userId,
      role: role ?? this.role,
      joinedAt: joinedAt ?? this.joinedAt,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Create a household member from JSON
  factory HouseholdMember.fromJson(Map<String, dynamic> json) {
    return HouseholdMember(
      id: json['id'] as String,
      householdId: json['household_id'] as String,
      userId: json['user_id'] as String,
      role: json['role'] as String,
      joinedAt: DateTime.parse(json['joined_at'] as String),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  /// Convert household member to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'household_id': householdId,
      'user_id': userId,
      'role': role,
      'joined_at': joinedAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  @override
  List<Object?> get props => [
        id,
        householdId,
        userId,
        role,
        joinedAt,
        isActive,
      ];

  @override
  String toString() {
    return 'HouseholdMember(id: $id, householdId: $householdId, userId: $userId, role: $role)';
  }
}
