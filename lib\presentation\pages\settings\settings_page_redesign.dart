import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../widgets/common/form_components.dart';

/// Redesigned settings page with modern UI
class SettingsPageRedesign extends StatefulWidget {
  const SettingsPageRedesign({super.key});

  @override
  State<SettingsPageRedesign> createState() => _SettingsPageRedesignState();
}

class _SettingsPageRedesignState extends State<SettingsPageRedesign> {
  // Removed unused variables for hidden sections
  String _appVersion = 'Version 0.5.1+1';

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
  }

  Future<void> _loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion =
            'Version ${packageInfo.version}+${packageInfo.buildNumber}';
      });
    } catch (e) {
      // Keep default version if package info fails
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.pop(),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Paramètres',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Profile section
                    _buildProfileSection(),

                    const SizedBox(height: 32),

                    // Settings sections - Notification, Apparence, and Langue sections hidden as requested
                    // _buildNotificationSettings(),
                    // const SizedBox(height: 24),
                    // _buildAppearanceSettings(),
                    // const SizedBox(height: 24),
                    // _buildLanguageSettings(),
                    // const SizedBox(height: 24),

                    // Account management moved to dedicated profile page
                    // _buildAccountSettings(),
                    // const SizedBox(height: 24),

                    _buildPersonalizationSection(),

                    const SizedBox(height: 24),

                    // Support section removed - version display added at bottom
                    // _buildSupportSettings(),
                    // const SizedBox(height: 24),

                    // Debug settings (only in debug builds)
                    if (kDebugMode) _buildDebugSettings(),

                    const SizedBox(height: 32),

                    // Simple version display
                    Center(
                      child: Text(
                        _appVersion,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return BlocBuilder<AuthBloc, auth_state.AuthState>(
      builder: (context, authState) {
        String? userName;
        String? userEmail;
        String? avatarUrl;

        if (authState is auth_state.AuthAuthenticated) {
          userName = authState.user.displayName ?? 'Utilisateur';
          userEmail = authState.user.email;
          avatarUrl = null; // User entity doesn't have photoURL property
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.tealLight,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.teal.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: avatarUrl != null
                    ? ClipOval(
                        child: Image.network(
                          avatarUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildDefaultAvatar(),
                        ),
                      )
                    : _buildDefaultAvatar(),
              ),

              const SizedBox(width: 20),

              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName ?? 'Utilisateur',
                      style: AppTextStyles.titleLarge.copyWith(
                        color: AppColors.navy,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (userEmail != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        userEmail,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ModernButton(
                            text: 'Modifier le profil',
                            isOutlined: true,
                            onPressed: () {
                              context.push('/profile');
                            },
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ModernButton(
                            text: 'Se déconnecter',
                            isOutlined: false,
                            backgroundColor: AppColors.error,
                            onPressed: () {
                              _showLogoutConfirmation();
                            },
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      color: AppColors.teal,
      size: 40,
    );
  }

  // Removed unused methods: _buildNotificationSettings, _buildAppearanceSettings, _buildLanguageSettings
  // These sections are hidden as requested

  // Removed _buildAccountSettings - account management moved to profile page

  Widget _buildPersonalizationSection() {
    return _buildSettingsSection(
      title: 'Personnalisation',
      icon: Icons.tune_outlined,
      children: [
        _buildActionTile(
          title: 'Gestion de Famille',
          subtitle: 'Gérer les membres de votre famille',
          icon: Icons.family_restroom_outlined,
          onTap: () {
            context.push('/family');
          },
        ),
        _buildActionTile(
          title: 'Emplacements',
          subtitle: 'Gérer les lieux de stockage',
          icon: Icons.location_on_outlined,
          onTap: () {
            context.push('/locations');
          },
        ),
      ],
    );
  }

  // Removed _buildSupportSettings - version display moved to bottom of page

  Widget _buildDebugSettings() {
    return _buildSettingsSection(
      title: 'Développement',
      icon: Icons.bug_report_outlined,
      children: [
        _buildActionTile(
          title: 'Debug Tools',
          subtitle: 'Test crash, error tracking, and system info',
          icon: Icons.bug_report,
          onTap: () {
            context.push('/debug');
          },
        ),
        _buildActionTile(
          title: 'Debug Rappels',
          subtitle: 'Tester les actions de rappel et historique',
          icon: Icons.medication_outlined,
          onTap: () {
            context.push('/debug/reminders');
          },
        ),
        _buildActionTile(
          title: 'Debug Médicaments',
          subtitle: 'Tester l\'ajout et la gestion des médicaments',
          icon: Icons.add_box_outlined,
          onTap: () {
            context.push('/debug/add-medicine');
          },
        ),
        _buildActionTile(
          title: 'Debug Cartes',
          subtitle: 'Tester l\'affichage des cartes médicaments',
          icon: Icons.credit_card_outlined,
          onTap: () {
            context.push('/debug/medicine-cards');
          },
        ),
        _buildActionTile(
          title: 'Nouveau flux rappel (Test)',
          subtitle: 'Ouvrir le flow multi-étapes de création de rappel',
          icon: Icons.alarm_add_outlined,
          onTap: () {
            context.push('/debug/add-reminder-test');
          },
        ),
      ],
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.tealLight.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.teal,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Section content
          ...children,
        ],
      ),
    );
  }

  // Removed unused helper methods: _buildSwitchTile, _buildDropdownTile
  // These were used by the hidden sections

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.teal,
        size: 24,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          color: AppColors.navy,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.grey600,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.grey400,
        size: 16,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Déconnexion'),
          content: const Text('Êtes-vous sûr de vouloir vous déconnecter ?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Annuler'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.read<AuthBloc>().add(AuthSignOutRequested());
                context.go('/auth');
              },
              child: Text(
                'Se déconnecter',
                style: TextStyle(color: AppColors.error),
              ),
            ),
          ],
        );
      },
    );
  }

  // Removed _showAboutDialog - no longer needed with simplified version display
}
