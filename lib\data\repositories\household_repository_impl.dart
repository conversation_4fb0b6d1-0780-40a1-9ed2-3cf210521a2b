import 'package:dartz/dartz.dart';
import '../../domain/entities/household.dart';
import '../../domain/repositories/household_repository.dart';
import '../../core/error/failures.dart';

/// Mock implementation of household repository for development
/// TODO: Replace with actual Supabase implementation
class HouseholdRepositoryImpl implements HouseholdRepository {
  @override
  Future<Either<Failure, Household>> createHousehold({
    required String name,
    String? description,
    required String ownerId,
  }) async {
    try {
      // Mock delay
      await Future.delayed(const Duration(seconds: 1));
      
      // Mock household creation
      final household = Household(
        id: 'mock-household-${DateTime.now().millisecondsSinceEpoch}',
        name: name,
        description: description,
        inviteCode: 'ABC123',
        ownerId: ownerId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isActive: true,
      );
      
      return Right(household);
    } catch (e) {
      return Left(ServerFailure('Failed to create household: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Household>> getHouseholdById(String householdId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final household = Household(
        id: householdId,
        name: 'Mock Household',
        description: 'A mock household for testing',
        inviteCode: 'ABC123',
        ownerId: 'mock-owner-id',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
        isActive: true,
      );
      
      return Right(household);
    } catch (e) {
      return Left(ServerFailure('Failed to get household: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Household>> getHouseholdByInviteCode(String inviteCode) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      if (inviteCode.toUpperCase() == 'ABC123') {
        final household = Household(
          id: 'mock-household-123',
          name: 'Famille Martin',
          description: 'Foyer de la famille Martin',
          inviteCode: inviteCode.toUpperCase(),
          ownerId: 'mock-owner-id',
          createdAt: DateTime.now().subtract(const Duration(days: 5)),
          updatedAt: DateTime.now(),
          isActive: true,
        );
        
        return Right(household);
      } else {
        return Left(ServerFailure('Code d\'invitation invalide'));
      }
    } catch (e) {
      return Left(ServerFailure('Failed to get household by invite code: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, Household>> updateHousehold({
    required String householdId,
    String? name,
    String? description,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final household = Household(
        id: householdId,
        name: name ?? 'Updated Household',
        description: description,
        inviteCode: 'ABC123',
        ownerId: 'mock-owner-id',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        updatedAt: DateTime.now(),
        isActive: true,
      );
      
      return Right(household);
    } catch (e) {
      return Left(ServerFailure('Failed to update household: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, String>> generateInviteCode(String householdId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Right('XYZ789');
    } catch (e) {
      return Left(ServerFailure('Failed to generate invite code: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> deactivateHousehold(String householdId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to deactivate household: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, HouseholdMember>> joinHousehold({
    required String userId,
    required String inviteCode,
  }) async {
    try {
      await Future.delayed(const Duration(seconds: 1));
      
      if (inviteCode.toUpperCase() == 'ABC123') {
        final member = HouseholdMember(
          id: 'mock-member-${DateTime.now().millisecondsSinceEpoch}',
          householdId: 'mock-household-123',
          userId: userId,
          role: 'member',
          joinedAt: DateTime.now(),
          isActive: true,
        );
        
        return Right(member);
      } else {
        return Left(ServerFailure('Code d\'invitation invalide'));
      }
    } catch (e) {
      return Left(ServerFailure('Failed to join household: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> leaveHousehold({
    required String userId,
    required String householdId,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to leave household: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<HouseholdMember>>> getHouseholdMembers(String householdId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final members = [
        HouseholdMember(
          id: 'member-1',
          householdId: householdId,
          userId: 'user-1',
          role: 'owner',
          joinedAt: DateTime.now().subtract(const Duration(days: 10)),
          isActive: true,
        ),
        HouseholdMember(
          id: 'member-2',
          householdId: householdId,
          userId: 'user-2',
          role: 'member',
          joinedAt: DateTime.now().subtract(const Duration(days: 5)),
          isActive: true,
        ),
      ];
      
      return Right(members);
    } catch (e) {
      return Left(ServerFailure('Failed to get household members: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, HouseholdMember>> updateMemberRole({
    required String memberId,
    required String newRole,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final member = HouseholdMember(
        id: memberId,
        householdId: 'mock-household-123',
        userId: 'mock-user-id',
        role: newRole,
        joinedAt: DateTime.now().subtract(const Duration(days: 5)),
        isActive: true,
      );
      
      return Right(member);
    } catch (e) {
      return Left(ServerFailure('Failed to update member role: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, void>> removeMember(String memberId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      return const Right(null);
    } catch (e) {
      return Left(ServerFailure('Failed to remove member: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, List<Household>>> getUserHouseholds(String userId) async {
    try {
      await Future.delayed(const Duration(milliseconds: 500));
      
      final households = [
        Household(
          id: 'household-1',
          name: 'Mon Foyer',
          description: 'Foyer principal',
          inviteCode: 'ABC123',
          ownerId: userId,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now(),
          isActive: true,
        ),
      ];
      
      return Right(households);
    } catch (e) {
      return Left(ServerFailure('Failed to get user households: ${e.toString()}'));
    }
  }

  @override
  Future<Either<Failure, bool>> isUserMemberOfHousehold({
    required String userId,
    required String householdId,
  }) async {
    try {
      await Future.delayed(const Duration(milliseconds: 300));
      return const Right(true);
    } catch (e) {
      return Left(ServerFailure('Failed to check membership: ${e.toString()}'));
    }
  }
}
