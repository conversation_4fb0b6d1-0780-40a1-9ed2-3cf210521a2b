import 'package:dartz/dartz.dart';
import '../../entities/household.dart';
import '../../repositories/household_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

/// Use case for joining a household using invite code
class JoinHouseholdUseCase implements UseCase<HouseholdMember, JoinHouseholdParams> {
  final HouseholdRepository repository;

  JoinHouseholdUseCase(this.repository);

  @override
  Future<Either<Failure, HouseholdMember>> call(JoinHouseholdParams params) async {
    return await repository.joinHousehold(
      userId: params.userId,
      inviteCode: params.inviteCode,
    );
  }
}

/// Parameters for joining a household
class JoinHouseholdParams {
  final String userId;
  final String inviteCode;

  const JoinHouseholdParams({
    required this.userId,
    required this.inviteCode,
  });
}
