# MedyTrack Development Environment Configuration
# This file contains configuration for the MedyTrack (Dev-New) Supabase project
# Updated: 2025-09-13 - Complete database clone from Production

# Supabase Configuration - Development (New Clone)
SUPABASE_URL=https://qzzqbddhogtmaldttmip.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF6enFiZGRob2d0bWFsZHR0bWlwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTc3NTk5ODQsImV4cCI6MjA3MzMzNTk4NH0.KMEeRFXV0LQq9ZxR9Ddl7R0PiLorLigNWSNVd4-fBN8

# Environment
ENVIRONMENT=development
DEBUG_MODE=true

# App Configuration
APP_NAME=MedyTrack Mobile (Dev)
APP_VERSION=0.5.1
APP_BUILD_NUMBER=1

# API Configuration
API_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3

# Feature Flags - Development
ENABLE_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=false
ENABLE_DEBUG_PAGE=true
ENABLE_VERBOSE_LOGGING=true

# Development Tools
ENABLE_FLUTTER_INSPECTOR=true
ENABLE_PERFORMANCE_OVERLAY=false
SHOW_SEMANTIC_DEBUGGER=false
