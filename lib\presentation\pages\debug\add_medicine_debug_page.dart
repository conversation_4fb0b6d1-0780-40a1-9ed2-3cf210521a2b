import 'package:flutter/material.dart';
import 'dart:async';
import '../../../core/utils/supabase_utils.dart';
import '../../../core/di/injection_container.dart';
import '../../../domain/entities/tunisia_medicine.dart';
import '../../../domain/repositories/tunisia_medicine_repository.dart';
import '../../../data/models/tunisia_medicine_model.dart';
import '../../widgets/common/modern_header.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Dedicated debug page for testing medicine addition functionality
/// Implements the dual-path workflow (database search vs custom entry)
class AddMedicineDebugPage extends StatefulWidget {
  const AddMedicineDebugPage({super.key});

  @override
  State<AddMedicineDebugPage> createState() => _AddMedicineDebugPageState();
}

class _AddMedicineDebugPageState extends State<AddMedicineDebugPage> {
  // Search state
  final TextEditingController _searchController = TextEditingController();
  Timer? _searchDebounceTimer;
  List<TunisiaMedicine> _searchResults = [];
  bool _isSearching = false;

  // Selection state
  TunisiaMedicine? _selectedMedicine;
  bool _isCustomMode = false;
  String? _customMedicineName;

  // Console output for debugging
  final List<String> _consoleOutput = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _addToConsole('Add Medicine Debug Page initialized');
    _addToConsole('Ready for testing dual-path workflow...');
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchDebounceTimer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _addToConsole(String message) {
    setState(() {
      _consoleOutput.add(
          '${DateTime.now().toIso8601String().substring(11, 19)}: $message');
    });
    // Auto-scroll to bottom
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  // Search functionality with 300ms debounce
  void _onSearchChanged(String query) {
    _searchDebounceTimer?.cancel();

    if (query.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    _searchDebounceTimer = Timer(const Duration(milliseconds: 300), () async {
      try {
        final tunisiaMedicineRepo = getIt<TunisiaMedicineRepository>();
        final results = await tunisiaMedicineRepo.searchMedicines(
          query.trim(),
          limit: 10,
        );

        if (mounted) {
          setState(() {
            _searchResults = results;
            _isSearching = false;
          });
          _addToConsole(
              'Search for "$query" returned ${results.length} results');
        }
      } catch (e) {
        if (mounted) {
          setState(() {
            _searchResults = [];
            _isSearching = false;
          });
          _addToConsole('Search error: $e');
        }
      }
    });
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() {
      _searchResults = [];
      _isSearching = false;
      _selectedMedicine = null;
      _isCustomMode = false;
      _customMedicineName = null;
    });
    _addToConsole('Search cleared - reset to initial state');
  }

  void _selectMedicine(TunisiaMedicine medicine) {
    setState(() {
      _selectedMedicine = medicine;
      _isCustomMode = false;
      _customMedicineName = null;
    });
    _addToConsole('Selected medicine: ${medicine.nom} (ID: ${medicine.id})');
    _addToConsole('Database path: is_custom=false, medicine_id=${medicine.id}');
  }

  void _switchToCustomMode() {
    setState(() {
      _isCustomMode = true;
      _selectedMedicine = null;
      _customMedicineName = null;
    });
    _addToConsole('Switched to custom medicine mode');
  }

  void _switchToSearchMode() {
    setState(() {
      _isCustomMode = false;
      _customMedicineName = null;
    });
    _addToConsole('Switched back to search mode');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Column(
        children: [
          // Custom Header
          _buildHeader(context),

          // Scrollable content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSearchSection(),
                  const SizedBox(height: 16),
                  _buildResultsSection(),
                  const SizedBox(height: 16),
                  _buildSelectedMedicineSection(),
                  const SizedBox(height: 16),
                  _buildCustomMedicineSection(),
                  const SizedBox(height: 16),
                  _buildConsoleSection(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppColors.teal.withValues(alpha: 0.1),
            Colors.white,
          ],
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(32),
          bottomRight: Radius.circular(32),
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
          child: Column(
            children: [
              // Back button and title row
              Row(
                children: [
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.arrow_back,
                      color: AppColors.navy,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Add Medicine Debug',
                      style: AppTextStyles.titleLarge.copyWith(
                        color: AppColors.navy,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // Description card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.08),
                      blurRadius: 20,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Test Dual-Path Workflow',
                      style: AppTextStyles.titleMedium.copyWith(
                        color: AppColors.navy,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Search tunisia_medicines database or add custom medicines. Test the complete medicine addition workflow.',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Medicine Search',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.navy,
            ),
          ),
          const SizedBox(height: 12),
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              labelText: 'Search Tunisia Medicines',
              hintText: 'Type medicine name (e.g., paracetamol)...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon: _isSearching
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: Padding(
                        padding: EdgeInsets.all(12),
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    )
                  : _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: _clearSearch,
                        )
                      : null,
              border: const OutlineInputBorder(),
            ),
            onChanged: _onSearchChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildResultsSection() {
    if (_searchResults.isEmpty &&
        !_isSearching &&
        _searchController.text.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Search Results',
            style: AppTextStyles.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.navy,
            ),
          ),
          const SizedBox(height: 12),
          if (_searchResults.isNotEmpty) ...[
            Container(
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: ListView.builder(
                itemCount: _searchResults.length,
                itemBuilder: (context, index) {
                  final medicine = _searchResults[index];
                  final isSelected = _selectedMedicine?.id == medicine.id;

                  return ListTile(
                    selected: isSelected,
                    selectedTileColor: AppColors.teal.withOpacity(0.1),
                    title: Text(medicine.nom),
                    subtitle: Text(
                      'Dosage: ${medicine.dosage ?? "N/A"} | '
                      'Forme: ${medicine.forme ?? "N/A"} | '
                      'Lab: ${medicine.laboratoire ?? "N/A"}',
                      style: AppTextStyles.bodySmall,
                    ),
                    onTap: () => _selectMedicine(medicine),
                    trailing: isSelected
                        ? Icon(Icons.check_circle, color: AppColors.teal)
                        : null,
                  );
                },
              ),
            ),
          ] else if (_searchController.text.isNotEmpty && !_isSearching) ...[
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                border: Border.all(color: Colors.orange.shade300),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange.shade700),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'No medicines found',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ElevatedButton(
                          onPressed: _switchToCustomMode,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('Add Custom Medicine'),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSelectedMedicineSection() {
    if (_selectedMedicine == null) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: AppColors.success, size: 24),
              const SizedBox(width: 8),
              Text(
                'Selected Medicine',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.success.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: AppColors.success.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _selectedMedicine!.nom,
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppColors.success,
                  ),
                ),
                const SizedBox(height: 8),
                _buildDetailRow('ID', _selectedMedicine!.id),
                _buildDetailRow('Dosage', _selectedMedicine!.dosage ?? 'N/A'),
                _buildDetailRow('Forme', _selectedMedicine!.forme ?? 'N/A'),
                _buildDetailRow(
                    'Laboratoire', _selectedMedicine!.laboratoire ?? 'N/A'),
                _buildDetailRow('DCI', _selectedMedicine!.dci ?? 'N/A'),
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppColors.navy.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Database Storage:',
                        style: AppTextStyles.bodySmall.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.navy,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text('• is_custom: false',
                          style: AppTextStyles.bodySmall),
                      Text('• medicine_id: ${_selectedMedicine!.id}',
                          style: AppTextStyles.bodySmall),
                      Text('• custom_name: null',
                          style: AppTextStyles.bodySmall),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCustomMedicineSection() {
    if (!_isCustomMode) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.add_circle, color: AppColors.teal, size: 24),
              const SizedBox(width: 8),
              Text(
                'Custom Medicine Mode',
                style: AppTextStyles.titleMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.teal,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          TextField(
            decoration: const InputDecoration(
              labelText: 'Custom Medicine Name',
              hintText: 'Enter your custom medicine name...',
              border: OutlineInputBorder(),
            ),
            onChanged: (value) {
              setState(() {
                _customMedicineName = value;
              });
              if (value.isNotEmpty) {
                _addToConsole('Custom medicine name: "$value"');
                _addToConsole('Custom path: is_custom=true, medicine_id=null');
              }
            },
          ),
          if (_customMedicineName != null &&
              _customMedicineName!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.teal.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.teal.withOpacity(0.3)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Custom Medicine: "$_customMedicineName"',
                    style: AppTextStyles.titleSmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.teal,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: AppColors.navy.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Database Storage:',
                          style: AppTextStyles.bodySmall.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.navy,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text('• is_custom: true',
                            style: AppTextStyles.bodySmall),
                        Text('• medicine_id: null',
                            style: AppTextStyles.bodySmall),
                        Text('• custom_name: "$_customMedicineName"',
                            style: AppTextStyles.bodySmall),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
          const SizedBox(height: 12),
          ElevatedButton(
            onPressed: _switchToSearchMode,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.grey400,
              foregroundColor: Colors.white,
            ),
            child: const Text('Back to Search Mode'),
          ),
        ],
      ),
    );
  }

  Widget _buildConsoleSection() {
    return Container(
      height: 200,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.black87,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade600),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.terminal, color: Colors.white, size: 16),
              const SizedBox(width: 8),
              Text(
                'Debug Console',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _consoleOutput.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.symmetric(vertical: 1),
                  child: Text(
                    _consoleOutput[index],
                    style: const TextStyle(
                      color: Colors.greenAccent,
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: AppTextStyles.bodySmall.copyWith(
                fontWeight: FontWeight.w500,
                color: AppColors.grey600,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.grey800,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
