import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../utils/logger.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;

/// New streamlined 2-page onboarding flow for household setup and app introduction
class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final PageController _pageController = PageController();
  final TextEditingController _householdNameController =
      TextEditingController(text: '<PERSON><PERSON>');
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  int _currentPage = 0;
  bool _isLoading = false;

  @override
  void dispose() {
    _pageController.dispose();
    _householdNameController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _createHousehold({bool skipToFeatures = false}) {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    AppLogger.log(
        '🏠 Creating household: ${_householdNameController.text.trim()}');

    context.read<AuthBloc>().add(
          AuthCreateHouseholdRequested(
            householdName: _householdNameController.text.trim(),
          ),
        );

    if (skipToFeatures) {
      // Skip to features page
      _nextPage();
    }
  }

  void _skipToApp() {
    setState(() {
      _isLoading = true;
    });

    AppLogger.log('🏠 Skipping onboarding, creating default household');

    context.read<AuthBloc>().add(
          const AuthCreateHouseholdRequested(householdName: 'Mon Foyer'),
        );
  }

  void _openQRScanner() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _QRScannerModal(
        onCodeScanned: _handleQRCode,
      ),
    );
  }

  void _handleQRCode(String code) {
    Navigator.of(context).pop(); // Close scanner modal
    AppLogger.log('📱 QR Code scanned: $code');

    // TODO: Implement household invite validation and joining
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Code QR détecté: $code'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _completeOnboarding() {
    final authState = context.read<AuthBloc>().state;

    if (authState is auth_state.AuthAuthenticated) {
      AppLogger.log('✅ Onboarding completed, navigating to dashboard');
      context.go('/dashboard');
    } else {
      AppLogger.log(
          '⚠️ User not authenticated yet, waiting for authentication...');
      // The BlocListener will handle navigation when authentication completes
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthBloc, auth_state.AuthState>(
      listener: (context, state) {
        if (state is auth_state.AuthAuthenticated) {
          // Household created successfully
          setState(() {
            _isLoading = false;
          });

          if (_currentPage == 0) {
            // Move to features page
            _nextPage();
          } else if (_currentPage == 1) {
            // User is on features page and authentication is complete
            // Navigate to dashboard automatically
            AppLogger.log('✅ Authentication complete, navigating to dashboard');
            context.go('/dashboard');
          }
        } else if (state is auth_state.AuthError) {
          setState(() {
            _isLoading = false;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(state.message),
              backgroundColor: AppColors.error,
            ),
          );
        }
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: PageView(
            controller: _pageController,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            children: [
              _buildHouseholdSetupPage(),
              _buildFeaturesPage(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHouseholdSetupPage() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Progress indicator
          _buildProgressIndicator(),

          const SizedBox(height: 32),

          // Illustration
          Expanded(
            flex: 2,
            child: Center(
              child: Image.asset(
                'assets/Illustrations/Household_Invite.png',
                height: 200,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    width: 200,
                    decoration: BoxDecoration(
                      color: AppColors.teal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.home_outlined,
                      size: 80,
                      color: AppColors.teal,
                    ),
                  );
                },
              ),
            ),
          ),

          // Content
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Text(
                  'Configurons votre foyer',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: AppColors.grey800,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                Text(
                  'Créez votre foyer pour commencer à gérer vos médicaments en famille.',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.grey600,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Household name form
                Form(
                  key: _formKey,
                  child: TextFormField(
                    controller: _householdNameController,
                    decoration: InputDecoration(
                      labelText: 'Nom du foyer',
                      hintText: 'Mon Foyer',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      prefixIcon: const Icon(Icons.home_outlined),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Veuillez entrer un nom pour votre foyer';
                      }
                      return null;
                    },
                  ),
                ),

                const SizedBox(height: 24),

                // Action buttons
                Column(
                  children: [
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: _isLoading
                            ? null
                            : () => _createHousehold(skipToFeatures: true),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.teal,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                height: 20,
                                width: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Colors.white),
                                ),
                              )
                            : const Text('Confirmer'),
                      ),
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: _isLoading ? null : _openQRScanner,
                        icon: const Icon(Icons.qr_code_scanner),
                        label: const Text('Rejoindre via QR'),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 12),
                    TextButton(
                      onPressed: _isLoading ? null : _skipToApp,
                      child: Text(
                        'Passer',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeaturesPage() {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          // Progress indicator
          _buildProgressIndicator(),

          const SizedBox(height: 32),

          // Illustration
          Expanded(
            flex: 2,
            child: Center(
              child: Image.asset(
                'assets/Illustrations/Welcome.png',
                height: 200,
                fit: BoxFit.contain,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 200,
                    width: 200,
                    decoration: BoxDecoration(
                      color: AppColors.teal.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      Icons.medical_services_outlined,
                      size: 80,
                      color: AppColors.teal,
                    ),
                  );
                },
              ),
            ),
          ),

          // Content
          Expanded(
            flex: 3,
            child: Column(
              children: [
                Text(
                  'Bienvenue dans MedyTrack',
                  style: AppTextStyles.headlineMedium.copyWith(
                    color: AppColors.grey800,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                Text(
                  'Gérez vos médicaments, configurez des rappels et partagez avec votre famille en toute sécurité.',
                  style: AppTextStyles.bodyLarge.copyWith(
                    color: AppColors.grey600,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 32),

                // Feature highlights
                _buildFeatureItem(
                  Icons.medication_outlined,
                  'Suivi des médicaments',
                  'Gardez une trace de tous vos médicaments et stocks',
                ),

                const SizedBox(height: 16),

                _buildFeatureItem(
                  Icons.notifications_outlined,
                  'Rappels intelligents',
                  'Ne manquez jamais une prise avec nos rappels personnalisés',
                ),

                const SizedBox(height: 16),

                _buildFeatureItem(
                  Icons.family_restroom_outlined,
                  'Partage familial',
                  'Gérez les médicaments de toute la famille en un seul endroit',
                ),

                const Spacer(),

                // Complete button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _completeOnboarding,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.teal,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text('Commencer'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _buildProgressDot(0),
        const SizedBox(width: 8),
        _buildProgressDot(1),
      ],
    );
  }

  Widget _buildProgressDot(int index) {
    final isActive = index == _currentPage;
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      width: isActive ? 24 : 8,
      height: 8,
      decoration: BoxDecoration(
        color: isActive ? AppColors.teal : AppColors.teal.withOpacity(0.3),
        borderRadius: BorderRadius.circular(4),
      ),
    );
  }

  Widget _buildFeatureItem(IconData icon, String title, String description) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppColors.teal.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: AppColors.teal,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: AppTextStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.grey800,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// QR Scanner Modal Widget
class _QRScannerModal extends StatefulWidget {
  final Function(String) onCodeScanned;

  const _QRScannerModal({required this.onCodeScanned});

  @override
  State<_QRScannerModal> createState() => _QRScannerModalState();
}

class _QRScannerModalState extends State<_QRScannerModal> {
  MobileScannerController controller = MobileScannerController();

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Scanner le code QR',
                  style: AppTextStyles.headlineSmall.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Scanner
          Expanded(
            child: Container(
              margin: const EdgeInsets.all(16),
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
              ),
              child: MobileScanner(
                controller: controller,
                onDetect: (capture) {
                  final List<Barcode> barcodes = capture.barcodes;
                  for (final barcode in barcodes) {
                    if (barcode.rawValue != null) {
                      widget.onCodeScanned(barcode.rawValue!);
                      break;
                    }
                  }
                },
              ),
            ),
          ),

          // Instructions
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Pointez votre caméra vers le code QR d\'invitation',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
