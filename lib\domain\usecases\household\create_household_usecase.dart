import 'package:dartz/dartz.dart';
import '../../entities/household.dart';
import '../../repositories/household_repository.dart';
import '../../../core/error/failures.dart';
import '../../../core/usecases/usecase.dart';

/// Use case for creating a new household
class CreateHouseholdUseCase implements UseCase<Household, CreateHouseholdParams> {
  final HouseholdRepository repository;

  CreateHouseholdUseCase(this.repository);

  @override
  Future<Either<Failure, Household>> call(CreateHouseholdParams params) async {
    return await repository.createHousehold(
      name: params.name,
      description: params.description,
      ownerId: params.ownerId,
    );
  }
}

/// Parameters for creating a household
class CreateHouseholdParams {
  final String name;
  final String? description;
  final String ownerId;

  const CreateHouseholdParams({
    required this.name,
    this.description,
    required this.ownerId,
  });
}
