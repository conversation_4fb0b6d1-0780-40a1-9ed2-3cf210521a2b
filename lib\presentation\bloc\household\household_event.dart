import 'package:equatable/equatable.dart';

/// Base class for household events
abstract class HouseholdEvent extends Equatable {
  const HouseholdEvent();

  @override
  List<Object?> get props => [];
}

/// Event to create a new household
class CreateHouseholdEvent extends HouseholdEvent {
  final String name;
  final String? description;
  final String ownerId;

  const CreateHouseholdEvent({
    required this.name,
    this.description,
    required this.ownerId,
  });

  @override
  List<Object?> get props => [name, description, ownerId];
}

/// Event to join a household using invite code
class JoinHouseholdEvent extends HouseholdEvent {
  final String userId;
  final String inviteCode;

  const JoinHouseholdEvent({
    required this.userId,
    required this.inviteCode,
  });

  @override
  List<Object?> get props => [userId, inviteCode];
}

/// Event to get household by invite code
class GetHouseholdByInviteCodeEvent extends HouseholdEvent {
  final String inviteCode;

  const GetHouseholdByInviteCodeEvent(this.inviteCode);

  @override
  List<Object?> get props => [inviteCode];
}

/// Event to validate invite code
class ValidateInviteCodeEvent extends HouseholdEvent {
  final String inviteCode;

  const ValidateInviteCodeEvent(this.inviteCode);

  @override
  List<Object?> get props => [inviteCode];
}

/// Event to reset household state
class ResetHouseholdEvent extends HouseholdEvent {
  const ResetHouseholdEvent();
}
