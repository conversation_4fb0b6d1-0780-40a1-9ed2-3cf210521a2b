# MedyTrack Production Environment Configuration
# This file contains configuration for the MedyTrack (Prod) Supabase project

# Supabase Configuration - Production
SUPABASE_URL=https://wzzykbnebhyvdoagpwvk.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6enlrYm5lYmh5dmRvYWdwd3ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwMTMzNjMsImV4cCI6MjA2MDU4OTM2M30.LYLDFRLZB4qtuzxTjRuZqnepRwrg3BNGLwdnwU4D0Es

# Environment
ENVIRONMENT=production
DEBUG_MODE=false

# App Configuration
APP_NAME=MedyTrack Mobile
APP_VERSION=0.5.1
APP_BUILD_NUMBER=1

# API Configuration
API_TIMEOUT_SECONDS=30
MAX_RETRY_ATTEMPTS=3

# Feature Flags - Production
ENABLE_NOTIFICATIONS=true
ENABLE_BIOMETRIC_AUTH=true
ENABLE_OFFLINE_MODE=true
ENABLE_ANALYTICS=true
ENABLE_DEBUG_PAGE=false
ENABLE_VERBOSE_LOGGING=false

# Production Security
ENABLE_FLUTTER_INSPECTOR=false
ENABLE_PERFORMANCE_OVERLAY=false
SHOW_SEMANTIC_DEBUGGER=false
