import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:medytrack_mobile/main.dart' as app;
import 'package:medytrack_mobile/core/config/environment_config.dart';

// Test configuration
const int performanceTimeoutMs = 2000;
const int maxScrollTestItems = 100;

/// Critical Workflows Integration Tests
/// These tests verify that all user-facing workflows function correctly
/// and prevent regression of previously fixed bugs.
void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Critical Workflows Integration Tests', () {
    setUpAll(() async {
      // Initialize environment for testing
      await EnvironmentConfig.initialize();
    });

    testWidgets('Add Medicine Workflow - Complete Flow',
        (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to Add Medicine page
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // Test medicine search and selection
      await tester.enterText(find.byType(TextField).first, 'Paracetamol');
      await tester.pumpAndSettle();

      // Verify search results appear
      expect(find.text('Paracetamol'), findsAtLeastNWidgets(1));

      // Select first medicine
      await tester.tap(find.text('Paracetamol').first);
      await tester.pumpAndSettle();

      // Fill in dosage information
      await tester.enterText(find.byKey(const Key('dosage_field')), '500');
      await tester.pumpAndSettle();

      // Select form (tablet)
      await tester.tap(find.byKey(const Key('form_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Tablet').last);
      await tester.pumpAndSettle();

      // Fill in quantity
      await tester.enterText(find.byKey(const Key('quantity_field')), '30');
      await tester.pumpAndSettle();

      // Set expiry date
      await tester.tap(find.byKey(const Key('expiry_date_picker')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      // Save medicine
      await tester.tap(find.byKey(const Key('save_medicine_button')));
      await tester.pumpAndSettle();

      // Verify success message or navigation
      expect(find.text('Medicine added successfully'), findsOneWidget);
    });

    testWidgets('Edit Medicine Workflow - Location Display Fix',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to medicine list
      await tester.tap(find.byIcon(Icons.medication));
      await tester.pumpAndSettle();

      // Find and tap on a medicine card
      final medicineCard = find.byType(Card).first;
      await tester.tap(medicineCard);
      await tester.pumpAndSettle();

      // Tap edit button
      await tester.tap(find.byIcon(Icons.edit));
      await tester.pumpAndSettle();

      // Verify location field shows human-readable name, not UUID
      final locationField = find.byKey(const Key('location_field'));
      expect(locationField, findsOneWidget);

      final locationText =
          tester.widget<TextField>(locationField).controller?.text ?? '';

      // Critical regression test: Ensure location shows name, not UUID
      expect(locationText.contains('-'), isFalse,
          reason: 'Location should show human-readable name, not UUID');
      expect(locationText.length, lessThan(50),
          reason: 'Location name should be reasonable length, not UUID');

      // Change location
      await tester.tap(find.byKey(const Key('location_dropdown')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Kitchen').last);
      await tester.pumpAndSettle();

      // Save changes
      await tester.tap(find.byKey(const Key('save_changes_button')));
      await tester.pumpAndSettle();

      // Verify location update
      expect(find.text('Medicine updated successfully'), findsOneWidget);
    });

    testWidgets('Reminder Scheduling Workflow', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to Add Reminder
      await tester.tap(find.byIcon(Icons.alarm_add));
      await tester.pumpAndSettle();

      // Select medicine
      await tester.tap(find.byKey(const Key('medicine_selector')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Paracetamol 500mg').first);
      await tester.pumpAndSettle();

      // Set dosage
      await tester.enterText(
          find.byKey(const Key('reminder_dosage_field')), '1');
      await tester.pumpAndSettle();

      // Set frequency
      await tester.tap(find.byKey(const Key('frequency_selector')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('Daily'));
      await tester.pumpAndSettle();

      // Set time
      await tester.tap(find.byKey(const Key('time_picker')));
      await tester.pumpAndSettle();
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();

      // Save reminder
      await tester.tap(find.byKey(const Key('save_reminder_button')));
      await tester.pumpAndSettle();

      // Verify reminder created
      expect(find.text('Reminder created successfully'), findsOneWidget);
    });

    testWidgets('Dose Tracking Workflow - Take/Skip/Snooze',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to dashboard
      await tester.tap(find.byIcon(Icons.dashboard));
      await tester.pumpAndSettle();

      // Find a reminder card with action buttons
      final reminderCard = find.byKey(const Key('reminder_card')).first;
      expect(reminderCard, findsOneWidget);

      // Test "Take" action
      await tester.tap(find.byKey(const Key('take_button')));
      await tester.pumpAndSettle();

      // Verify dose recorded
      expect(find.text('Dose recorded'), findsOneWidget);

      // Find another reminder for skip test
      final skipButton = find.byKey(const Key('skip_button'));
      if (skipButton.evaluate().isNotEmpty) {
        await tester.tap(skipButton);
        await tester.pumpAndSettle();
        expect(find.text('Dose skipped'), findsOneWidget);
      }

      // Test snooze functionality
      final snoozeButton = find.byKey(const Key('snooze_button'));
      if (snoozeButton.evaluate().isNotEmpty) {
        await tester.tap(snoozeButton);
        await tester.pumpAndSettle();

        // Select snooze duration
        await tester.tap(find.text('15 minutes'));
        await tester.pumpAndSettle();

        expect(find.text('Reminder snoozed'), findsOneWidget);
      }
    });

    testWidgets('Medicine List View - Location Names Display',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to medicine list
      await tester.tap(find.byIcon(Icons.medication));
      await tester.pumpAndSettle();

      // Verify medicine cards display location names, not UUIDs
      final medicineCards = find.byType(Card);
      expect(medicineCards, findsAtLeastNWidgets(1));

      // Check each medicine card for proper location display
      for (int i = 0; i < medicineCards.evaluate().length && i < 5; i++) {
        final card = medicineCards.at(i);
        final cardWidget = tester.widget<Card>(card);

        // Find location text within the card
        final locationFinder = find.descendant(
            of: card,
            matching: find.byKey(const Key('medicine_location_text')));

        if (locationFinder.evaluate().isNotEmpty) {
          final locationText = tester.widget<Text>(locationFinder).data ?? '';

          // Critical test: Location should not be a UUID
          expect(locationText.contains('-'), isFalse,
              reason:
                  'Medicine card $i shows UUID instead of location name: $locationText');
          expect(locationText.length, lessThan(50),
              reason:
                  'Location name should be reasonable length: $locationText');
        }
      }
    });

    testWidgets('Search Functionality - Medicine Search',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to medicine search
      await tester.tap(find.byIcon(Icons.search));
      await tester.pumpAndSettle();

      // Test search functionality
      await tester.enterText(find.byType(TextField), 'Para');
      await tester.pumpAndSettle();

      // Verify search results
      expect(find.text('Paracetamol'), findsAtLeastNWidgets(1));

      // Test empty search
      await tester.enterText(find.byType(TextField), '');
      await tester.pumpAndSettle();

      // Test invalid search
      await tester.enterText(find.byType(TextField), 'XYZ123NonExistent');
      await tester.pumpAndSettle();

      expect(find.text('No medicines found'), findsOneWidget);
    });

    testWidgets('Reminder Management - Pause/Archive/Activate',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to reminders
      await tester.tap(find.byIcon(Icons.alarm));
      await tester.pumpAndSettle();

      // Find a reminder card
      final reminderCard = find.byType(Card).first;
      await tester.longPress(reminderCard);
      await tester.pumpAndSettle();

      // Test pause functionality
      if (find.text('Pause').evaluate().isNotEmpty) {
        await tester.tap(find.text('Pause'));
        await tester.pumpAndSettle();
        expect(find.text('Reminder paused'), findsOneWidget);
      }

      // Test archive functionality
      await tester.longPress(reminderCard);
      await tester.pumpAndSettle();

      if (find.text('Archive').evaluate().isNotEmpty) {
        await tester.tap(find.text('Archive'));
        await tester.pumpAndSettle();
        expect(find.text('Reminder archived'), findsOneWidget);
      }
    });

    testWidgets('Performance Test - Large Medicine List',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to medicine list
      await tester.tap(find.byIcon(Icons.medication));
      await tester.pumpAndSettle();

      // Measure scroll performance
      final stopwatch = Stopwatch()..start();

      // Scroll through the list
      await tester.fling(find.byType(ListView), const Offset(0, -500), 1000);
      await tester.pumpAndSettle();

      stopwatch.stop();

      // Performance assertion - scrolling should be smooth
      expect(stopwatch.elapsedMilliseconds, lessThan(1000),
          reason: 'Medicine list scrolling should be performant');
    });
  });

  group('Regression Prevention Tests', () {
    testWidgets('Medicine Location Bug - Never Show UUIDs',
        (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // This test specifically prevents the regression of the location UUID bug
      await tester.tap(find.byIcon(Icons.medication));
      await tester.pumpAndSettle();

      // Check all visible medicine cards
      final medicineCards = find.byType(Card);

      for (int i = 0; i < medicineCards.evaluate().length; i++) {
        final card = medicineCards.at(i);

        // Look for any text that looks like a UUID
        final textWidgets =
            find.descendant(of: card, matching: find.byType(Text));

        for (int j = 0; j < textWidgets.evaluate().length; j++) {
          final textWidget = tester.widget<Text>(textWidgets.at(j));
          final text = textWidget.data ?? '';

          // UUID pattern check
          final uuidPattern = RegExp(
              r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$');

          expect(uuidPattern.hasMatch(text), isFalse,
              reason: 'Found UUID in medicine card: $text');
        }
      }
    });

    testWidgets('Debug Output Prevention - Production Build',
        (WidgetTester tester) async {
      // This test ensures no debug output in production builds
      // In a real scenario, this would check build artifacts

      app.main();
      await tester.pumpAndSettle();

      // Verify debug page is not accessible in production
      // This is a placeholder - actual implementation would depend on your debug page
      expect(find.byKey(const Key('debug_page_button')), findsNothing);
    });
  });
}
